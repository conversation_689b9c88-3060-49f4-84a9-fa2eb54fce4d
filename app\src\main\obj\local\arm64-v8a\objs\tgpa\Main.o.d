src/main/obj/local/arm64-v8a/objs/tgpa/Main.o: src/main/jni/Main.cpp \
  src/main/jni/Includes/obfuscate.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstddef \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__config \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string_view \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__string \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/algorithm \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/initializer_list \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstring \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/utility \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__tuple \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdint \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__debug \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/memory \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/typeinfo \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/exception \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdlib \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdlib.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/new \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/limits \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iterator \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iosfwd \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/wchar.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__functional_base \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/tuple \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdexcept \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cassert \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/atomic \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdio \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdio.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cwchar \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cwctype \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cctype \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ctype.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/wctype.h \
  src/main/jni/HMOD/Call_Me.h src/main/jni/ImGui/Call_ImGui.h \
  src/main/jni/ImGui/imgui.h src/main/jni/ImGui/imconfig.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stddef.h \
  src/main/jni/ImGui/imgui_impl_android.h \
  src/main/jni/ImGui/imgui_impl_opengl3.h src/main/jni/ImGui/Font.h \
  src/main/jni/ImGui/Icon.h src/main/jni/ImGui/Iconcpp.h \
  src/main/jni/ImGui/ImguiPP.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ctime \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/sstream \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ostream \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ios \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__locale \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/mutex \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__mutex_base \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/chrono \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ratio \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/climits \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/system_error \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cerrno \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/errno.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__threading_support \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/functional \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/locale.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/streambuf \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/locale \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdarg \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__bsd_locale_fallbacks.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/bitset \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__bit_reference \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/istream \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iomanip \
  src/main/jni/ImGui/stb_image.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/math.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Call_IL2CppSDKGenerator.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Includes.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iostream \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/inttypes.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/vector \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__split_buffer \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/map \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__tree \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/fstream \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/thread \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/codecvt \
  src/main/jni/HMOD/IL2CppSDKGenerator/IL2Cpp.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Vector2.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Vector3.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/VInt3.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Rect.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/Quaternion.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/KittyMemory.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/KittyUtils.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/MemoryBackup.h \
  src/main/jni/HMOD/IL2CppSDKGenerator/MemoryPatch.h \
  src/main/jni/HMOD/Tools/Call_Tools.h \
  src/main/jni/HMOD/Tools/curl/json.hpp \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/numeric \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/array \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/forward_list \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/unordered_map \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__hash_table \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cmath \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/valarray \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/clocale \
  src/main/jni/HMOD/Tools/Tools.h src/main/jni/HMOD/Tools/Dobby/dobby.h \
  src/main/jni/HMOD/Tools/Jni_Stuff.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curl.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlver.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlbuild.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlrules.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/easy.h \
  src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/multi.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/rsa.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/opensslconf.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/asn1.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/e_os2.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/bio.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/crypto.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/stack.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/safestack.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/opensslv.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/ossl_typ.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/symhacks.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/bn.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pem.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/evp.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/objects.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/obj_mac.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/x509.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/buffer.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/ec.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/dsa.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/dh.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/sha.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/x509_vfy.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/lhash.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pkcs7.h \
  src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pem2.h \
  src/main/jni/HMOD/Tools/ImGuiStuff.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/queue \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/deque \
  src/main/jni/HMOD/Tools/ESP.h \
  src/main/jni/HMOD/Tools/base64/base64.cpp \
  src/main/jni/HMOD/Tools/base64/base64.h \
  src/main/jni/HMOD/Tools/base64/bdvt.cpp \
  src/main/jni/HMOD/Tools/base64/bdvt.h src/main/jni/HMOD/ESP/Color.hpp \
  src/main/jni/HMOD/ESP/Alert.h src/main/jni/HMOD/ESP/EspManager.h \
  src/main/jni/HMOD/Hacks/Call_Hacks.h \
  src/main/jni/HMOD/Hacks/Init_Hacks.h \
  src/main/jni/HMOD/Hacks/StructSDK.h src/main/jni/HMOD/Hacks/Overlay.h \
  /data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/set \
  src/main/jni/HMOD/Hacks/Hooker.h \
  src/main/jni/Substrate/SubstrateHook.h src/main/jni/libzip/zip.h \
  src/main/jni/libzip/zipconf.h src/main/jni/Hooks.h

src/main/jni/Includes/obfuscate.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstddef:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__config:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string_view:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__string:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/algorithm:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/initializer_list:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstring:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/string.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/utility:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__tuple:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdint:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__debug:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/memory:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/typeinfo:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/exception:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdlib:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdlib.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/new:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/limits:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iterator:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iosfwd:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/wchar.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__functional_base:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/tuple:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdexcept:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cassert:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/atomic:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdio:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stdio.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cwchar:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cwctype:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cctype:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ctype.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/wctype.h:

src/main/jni/HMOD/Call_Me.h:

src/main/jni/ImGui/Call_ImGui.h:

src/main/jni/ImGui/imgui.h:

src/main/jni/ImGui/imconfig.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/stddef.h:

src/main/jni/ImGui/imgui_impl_android.h:

src/main/jni/ImGui/imgui_impl_opengl3.h:

src/main/jni/ImGui/Font.h:

src/main/jni/ImGui/Icon.h:

src/main/jni/ImGui/Iconcpp.h:

src/main/jni/ImGui/ImguiPP.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ctime:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/sstream:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ostream:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ios:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__locale:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/mutex:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__mutex_base:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/chrono:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/ratio:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/climits:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/system_error:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cerrno:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/errno.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__threading_support:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/functional:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/locale.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/streambuf:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/locale:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cstdarg:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__bsd_locale_fallbacks.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/bitset:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__bit_reference:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/istream:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iomanip:

src/main/jni/ImGui/stb_image.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/math.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Call_IL2CppSDKGenerator.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Includes.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/iostream:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/inttypes.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/vector:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__split_buffer:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/map:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__tree:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/fstream:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/thread:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/codecvt:

src/main/jni/HMOD/IL2CppSDKGenerator/IL2Cpp.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Vector2.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Vector3.h:

src/main/jni/HMOD/IL2CppSDKGenerator/VInt3.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Rect.h:

src/main/jni/HMOD/IL2CppSDKGenerator/Quaternion.h:

src/main/jni/HMOD/IL2CppSDKGenerator/KittyMemory.h:

src/main/jni/HMOD/IL2CppSDKGenerator/KittyUtils.h:

src/main/jni/HMOD/IL2CppSDKGenerator/MemoryBackup.h:

src/main/jni/HMOD/IL2CppSDKGenerator/MemoryPatch.h:

src/main/jni/HMOD/Tools/Call_Tools.h:

src/main/jni/HMOD/Tools/curl/json.hpp:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/numeric:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/array:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/forward_list:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/unordered_map:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/__hash_table:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/cmath:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/valarray:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/clocale:

src/main/jni/HMOD/Tools/Tools.h:

src/main/jni/HMOD/Tools/Dobby/dobby.h:

src/main/jni/HMOD/Tools/Jni_Stuff.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curl.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlver.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlbuild.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/curlrules.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/easy.h:

src/main/jni/HMOD/Tools/curl/curl-android-arm64-v8a/include/curl/multi.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/rsa.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/opensslconf.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/asn1.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/e_os2.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/bio.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/crypto.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/stack.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/safestack.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/opensslv.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/ossl_typ.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/symhacks.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/bn.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pem.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/evp.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/objects.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/obj_mac.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/x509.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/buffer.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/ec.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/dsa.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/dh.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/sha.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/x509_vfy.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/lhash.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pkcs7.h:

src/main/jni/HMOD/Tools/curl/openssl-android-arm64-v8a/include/openssl/pem2.h:

src/main/jni/HMOD/Tools/ImGuiStuff.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/queue:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/deque:

src/main/jni/HMOD/Tools/ESP.h:

src/main/jni/HMOD/Tools/base64/base64.cpp:

src/main/jni/HMOD/Tools/base64/base64.h:

src/main/jni/HMOD/Tools/base64/bdvt.cpp:

src/main/jni/HMOD/Tools/base64/bdvt.h:

src/main/jni/HMOD/ESP/Color.hpp:

src/main/jni/HMOD/ESP/Alert.h:

src/main/jni/HMOD/ESP/EspManager.h:

src/main/jni/HMOD/Hacks/Call_Hacks.h:

src/main/jni/HMOD/Hacks/Init_Hacks.h:

src/main/jni/HMOD/Hacks/StructSDK.h:

src/main/jni/HMOD/Hacks/Overlay.h:

/data/user/0/com.aide.clone/no_backup/ndksupport-1710240003/android-ndk-aide/sources/cxx-stl/llvm-libc++/include/set:

src/main/jni/HMOD/Hacks/Hooker.h:

src/main/jni/Substrate/SubstrateHook.h:

src/main/jni/libzip/zip.h:

src/main/jni/libzip/zipconf.h:

src/main/jni/Hooks.h:
