#include "AutoAim.h"

// Global variables definition
EntityInfo EnemyTarget;
bool AimElsu = false;
bool isCharging = false;
int mode = 0, aimType = 1, drawType = 2, SkillSlott = 0;

// Hook function pointers
Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse) = nullptr;
void (*_UpdateLogic)(void *instance, int delta) = nullptr;

// Utility functions
Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.W, x = q.X, y = q.Y, z = q.Z;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.X - o.X) * (v.X - o.X) + (v.Y - o.Y) * (v.Y - o.Y) + (v.Z - o.Z) * (v.Z - o.Z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {
        enemyPosi += moveForward;
    }
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

float getRange(int configID) {
    switch(configID) {
        case 196: return 25.f;  // Elsu
        case 108: return 13.f;  // Other heroes
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}

// Hook functions
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != nullptr && AimElsu) {
        if (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || 
            EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || 
            EnemyTarget.ConfigID == 545) {
            if (EnemyTarget.myPos != Vector3::Zero() && 
                EnemyTarget.enemyPos != Vector3::Zero() && 
                SkillSlott == 2) {
                return calculateSkillDirection(EnemyTarget.myPos, EnemyTarget.enemyPos, 
                                             EnemyTarget.isMoving, EnemyTarget.moveForward);
            }
        }
    }
    
    if (_GetUseSkillDirection != nullptr) {
        return _GetUseSkillDirection(instance, isTouchUse);
    }
    
    return Vector3::Zero();
}

void UpdateLogic(void *instance, int delta) {
    if (instance != nullptr) {
        // Get charging state and skill slot
        try {
            isCharging = *(bool *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_isCharging"));
            SkillSlott = *(int *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_currentSkillSlotType"));
        } catch (...) {
            // Handle potential memory access errors
            isCharging = false;
            SkillSlott = 0;
        }
    }
    
    if (AimElsu) {
        Quaternion rotation;
        float minDistance = std::numeric_limits<float>::infinity();
        float minDirection = std::numeric_limits<float>::infinity();
        float minHealth = std::numeric_limits<float>::infinity();
        float minHealth2 = std::numeric_limits<float>::infinity();
        float minHealthPercent = std::numeric_limits<float>::infinity();
        ActorLinker *Entity = nullptr;
        
        try {
            ActorManager *get_actorManager = KyriosFramework::get_actorManager();
            if (get_actorManager == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            List<ActorLinker *> *GetAllHeros = get_actorManager->GetAllHeros();
            if (GetAllHeros == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            ActorLinker **actorLinkers = (ActorLinker **) GetAllHeros->getItems();
            if (actorLinkers == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            for (int i = 0; i < GetAllHeros->getSize(); i++) {
                ActorLinker *actorLinker = actorLinkers[(i * 2) + 1];
                if (actorLinker == nullptr) continue;
            
                if (actorLinker->IsHostPlayer()) {
                    rotation = actorLinker->get_rotation();
                    EnemyTarget.myPos = actorLinker->get_position();
                    EnemyTarget.ConfigID = actorLinker->ObjLinker()->ConfigID();
                }
            
                if (actorLinker->IsHostCamp() || actorLinker->ValueComponent()->get_actorHp() < 1) continue;
            
                Vector3 EnemyPos = actorLinker->get_position();
                float Health = (float)actorLinker->ValueComponent()->get_actorHp();
                float MaxHealth = (float)actorLinker->ValueComponent()->get_actorHpTotal();
                
                if (MaxHealth <= 0) continue; // Avoid division by zero
                
                int HealthPercent = (int)std::round(Health / MaxHealth * 100);
                float Distance = Vector3::Distance(EnemyTarget.myPos, EnemyPos);
                float Direction = SquaredDistance(RotateVectorByQuaternion(rotation), 
                                                calculateSkillDirection(EnemyTarget.myPos, EnemyPos, 
                                                                      actorLinker->isMoving(), 
                                                                      actorLinker->get_logicMoveForward()));
                
                float range = getRange(EnemyTarget.ConfigID);
                if (Distance < range) {
                    if (aimType == 0) {
                        if (HealthPercent < minHealthPercent) {
                            Entity = actorLinker;
                            minHealthPercent = HealthPercent;
                        }
                    
                        if (HealthPercent == minHealthPercent && Health < minHealth2) {
                            Entity = actorLinker;
                            minHealth2 = Health;
                            minHealthPercent = HealthPercent;
                        }
                    }
                
                    if (aimType == 1 && Health < minHealth) {
                        Entity = actorLinker;
                        minHealth = Health;
                    }
                    
                    if (aimType == 2 && Distance < minDistance) {
                        Entity = actorLinker;
                        minDistance = Distance;
                    }
                
                    if (aimType == 3 && Direction < minDirection && isCharging) {
                        Entity = actorLinker;
                        minDirection = Direction;
                    }
                }
            }
        } catch (...) {
            // Handle any exceptions during target selection
            Entity = nullptr;
        }

        // Reset target if no valid entity found
        if (Entity == nullptr) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            EnemyTarget.ConfigID = 0;
            EnemyTarget.isMoving = false;
        }

        // Update target information
        if (Entity != nullptr) {
            try {
                float nDistance = Vector3::Distance(EnemyTarget.myPos, Entity->get_position());
                if (nDistance > getRange(EnemyTarget.ConfigID) || Entity->ValueComponent()->get_actorHp() < 1) {
                    EnemyTarget.enemyPos = Vector3::Zero();
                    EnemyTarget.moveForward = Vector3::Zero();
                    Entity = nullptr;
                } else {
                    EnemyTarget.enemyPos = Entity->get_position();
                    EnemyTarget.moveForward = Entity->get_logicMoveForward();
                    EnemyTarget.isMoving = Entity->isMoving();
                }
            } catch (...) {
                // Handle potential errors when accessing entity data
                EnemyTarget.enemyPos = Vector3::Zero();
                EnemyTarget.moveForward = Vector3::Zero();
                Entity = nullptr;
            }
        }
        
        // Special handling for aim type 3 (direction-based) when not charging
        if (Entity != nullptr && aimType == 3 && !isCharging) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            Entity = nullptr;
        }
    }
    
    // Call original function
    if (_UpdateLogic != nullptr) {
        _UpdateLogic(instance, delta);
    }
}

// Initialization function
void InitializeAutoAim() {
    // Initialize target info
    EnemyTarget.myPos = Vector3::Zero();
    EnemyTarget.enemyPos = Vector3::Zero();
    EnemyTarget.moveForward = Vector3::Zero();
    EnemyTarget.ConfigID = 0;
    EnemyTarget.isMoving = false;
    
    // Set default values
    AimElsu = false;
    isCharging = false;
    mode = 0;
    aimType = 1; // Default to lowest health
    drawType = 2;
    SkillSlott = 0;
    
    // Install hooks
    try {
        Tools::Hook((void *)(uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), 
                                                                     OBFUSCATE("Assets.Scripts.GameLogic"), 
                                                                     OBFUSCATE("SkillControlIndicator"), 
                                                                     OBFUSCATE("GetUseSkillDirection"), 1), 
                   (void *)GetUseSkillDirection, 
                   (void **)&_GetUseSkillDirection);

        Tools::Hook((void *)(uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), 
                                                                     OBFUSCATE("Assets.Scripts.GameSystem"), 
                                                                     OBFUSCATE("CSkillButtonManager"), 
                                                                     OBFUSCATE("UpdateLogic"), 1), 
                   (void *)UpdateLogic, 
                   (void **)&_UpdateLogic);
    } catch (...) {
        // Handle hook installation errors
    }
}
