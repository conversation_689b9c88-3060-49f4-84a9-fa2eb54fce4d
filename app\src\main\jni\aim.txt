#pragma once
#include <cmath>
#include <limits>
#include "HMOD/IL2CppSDKGenerator/Il2Cpp.h"
#include "HMOD/IL2CppSDKGenerator/Vector3.h"
#include "HMOD/IL2CppSDKGenerator/Quaternion.h"
#include "Includes/obfuscate.h"

#define OBFUSCATE_METHOD(image, namespaze, clazz, name, args) \
IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name), args)

#define OBFUSCATE_FIELD(image, namespaze, clazz, name) \
IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name))

class Camera {
    public:
        static Camera *get_main() {
        Camera *(*get_main_) () = (Camera *(*)())OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
        return get_main_();
    }

    Vector3 WorldToScreenPoint(Vector3 position) {
        Vector3 (*WorldToScreenPoint_)(Camera *camera, Vector3 position) = (Vector3 (*)(Camera *, Vector3))OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);
        return WorldToScreenPoint_(this, position);
    }
};

class ValueLinkerComponent {
    public:
        int get_actorHp() {
            int (*get_actorHp_)(ValueLinkerComponent * objLinkerWrapper) = (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHp", 0);
            return get_actorHp_(this);
        }

        int get_actorHpTotal() {
            int (*get_actorHpTotal_)(ValueLinkerComponent * objLinkerWrapper) =
                (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHpTotal", 0);
            return get_actorHpTotal_(this);
        }
};

class ActorConfig {
    public:
        int ConfigID() {
            return *(int *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "ActorConfig", "ConfigID"));
        }
};

class ActorLinker {
    public:
        ValueLinkerComponent *ValueComponent() {
            return *(ValueLinkerComponent **)((uintptr_t)this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ValueComponent"));
        }

        ActorConfig *ObjLinker() {
            return *(ActorConfig **) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ObjLinker"));
        }

        Vector3 get_position() {
            Vector3 (*get_position_)(ActorLinker * linker) = (Vector3(*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_position", 0);
            return get_position_(this);
        }

        Quaternion get_rotation() {
            Quaternion (*get_rotation_)(ActorLinker *linker) = (Quaternion (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_rotation", 0);
            return get_rotation_(this);
        }

        bool IsHostCamp() {
            bool (*IsHostCamp_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostCamp", 0);
            return IsHostCamp_(this);
        }

        bool IsHostPlayer() {
            bool (*IsHostPlayer_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostPlayer", 0);
            return IsHostPlayer_(this);
        }

        bool isMoving() {
            return *(bool *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "isMoving"));
        }

        Vector3 get_logicMoveForward() {
            Vector3 (*get_logicMoveForward_)(ActorLinker *linker) = (Vector3 (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_logicMoveForward", 0);
            return get_logicMoveForward_(this);
        }

        bool get_bVisible() {
            bool (*get_bVisible_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_bVisible", 0);
            return get_bVisible_(this);
        }
};

class ActorManager {
    public:
        List<ActorLinker *> *GetAllHeros() {
            List<ActorLinker *> *(*_GetAllHeros)(ActorManager *actorManager) = (List<ActorLinker *> *(*)(ActorManager *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorManager", "GetAllHeros", 0);
            return _GetAllHeros(this);
        }
};

class KyriosFramework {
    public:
        static ActorManager *get_actorManager() {
            auto get_actorManager_ = (ActorManager *(*)())OBFUSCATE_METHOD("Project_d.dll", "Kyrios", "KyriosFramework", "get_actorManager", 0);
            return get_actorManager_();
        }
};

struct EntityInfo {
    Vector3 myPos;
    Vector3 enemyPos;
    Vector3 moveForward;
    int ConfigID;
    bool isMoving;
};

EntityInfo EnemyTarget;

Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.W, x = q.X, y = q.Y, z = q.Z;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.X - o.X) * (v.X - o.X) + (v.Y - o.Y) * (v.Y - o.Y) + (v.Z - o.Z) * (v.Z - o.Z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {enemyPosi += moveForward;}
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

bool AimElsu;
bool isCharging;
int mode = 0, aimType = 1, drawType = 2, SkillSlott;



float getRange(int configID) {
    switch(configID) {
        case 196: return 25.f;
        case 108: return 13.f;
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}



Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse);
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != NULL && AimElsu) {
        if (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || EnemyTarget.ConfigID == 545) {
            if (EnemyTarget.myPos != Vector3::Zero() && EnemyTarget.enemyPos != Vector3::Zero() && SkillSlott == 2) {
                return calculateSkillDirection(EnemyTarget.myPos, EnemyTarget.enemyPos, EnemyTarget.isMoving, EnemyTarget.moveForward);
            }
        }
    }
    return _GetUseSkillDirection(instance, isTouchUse);
}

uintptr_t m_isCharging, m_currentSkillSlottType;
void (*_UpdateLogic)(void *instance, int delta);
void UpdateLogic(void *instance, int delta) {
     if (instance != NULL) {
        isCharging = *(bool *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_isCharging"));
            SkillSlott = *(int *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_currentSkillSlotType"));
    }
    if (AimElsu) {
        Quaternion rotation;
        float minDistance = std::numeric_limits<float>::infinity();
        float minDirection = std::numeric_limits<float>::infinity();
        float minHealth = std::numeric_limits<float>::infinity();
        float minHealth2 = std::numeric_limits<float>::infinity();
        float minHealthPercent = std::numeric_limits<float>::infinity();
        ActorLinker *Entity = nullptr;

        ActorManager *get_actorManager = KyriosFramework::get_actorManager();
        if (get_actorManager == nullptr) return;

        List<ActorLinker *> *GetAllHeros = get_actorManager->GetAllHeros();
        if (GetAllHeros == nullptr) return;

        ActorLinker **actorLinkers = (ActorLinker **) GetAllHeros->getItems();

        for (int i = 0; i < GetAllHeros->getSize(); i++) {
            ActorLinker *actorLinker = actorLinkers[(i *2) + 1];
            if (actorLinker == nullptr) continue;

            if (actorLinker->IsHostPlayer()) {
                rotation = actorLinker->get_rotation();
                EnemyTarget.myPos = actorLinker->get_position();
                EnemyTarget.ConfigID = actorLinker->ObjLinker()->ConfigID();
            }

            if (actorLinker->IsHostCamp() || actorLinker->ValueComponent()->get_actorHp() < 1) continue;

            Vector3 EnemyPos = actorLinker->get_position();
            float Health = actorLinker->ValueComponent()->get_actorHp();
            float MaxHealth = actorLinker->ValueComponent()->get_actorHpTotal();
            int HealthPercent = (int)std::round((float)Health / MaxHealth * 100);
            float Distance = Vector3::Distance(EnemyTarget.myPos, EnemyPos);
            float Direction = SquaredDistance(RotateVectorByQuaternion(rotation), calculateSkillDirection(EnemyTarget.myPos, EnemyPos, actorLinker->isMoving(), actorLinker->get_logicMoveForward()));

            float range = getRange(EnemyTarget.ConfigID);
            if (Distance < range) {
                if (aimType == 0) {
                    if (HealthPercent < minHealthPercent) {
                        Entity = actorLinker;
                        minHealthPercent = HealthPercent;
                    }

                    if (HealthPercent == minHealthPercent && Health < minHealth2) {
                        Entity = actorLinker;
                        minHealth2 = Health;
                        minHealthPercent = HealthPercent;
                    }
                }

                if (aimType == 1 && Health < minHealth) {
                    Entity = actorLinker;
                    minHealth = Health;
                }

                if (aimType == 2 && Distance < minDistance) {
                    Entity = actorLinker;
                    minDistance = Distance;
                }

                if (aimType == 3 && Direction < minDirection && isCharging) {
                    Entity = actorLinker;
                    minDirection = Direction;
                }
            }
        }

        if (Entity == nullptr) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            EnemyTarget.ConfigID = 0;
            EnemyTarget.isMoving = false;
        }

        if (Entity != NULL) {
            float nDistance = Vector3::Distance(EnemyTarget.myPos, Entity->get_position());
            if (nDistance > getRange(EnemyTarget.ConfigID) || Entity->ValueComponent()->get_actorHp() < 1) {
                EnemyTarget.enemyPos = Vector3::Zero();
                EnemyTarget.moveForward = Vector3::Zero();
                minDistance = std::numeric_limits<float>::infinity();
                minDirection = std::numeric_limits<float>::infinity();
                minHealth = std::numeric_limits<float>::infinity();
                minHealth2 = std::numeric_limits<float>::infinity();
                minHealthPercent = std::numeric_limits<float>::infinity();
                Entity = nullptr;
            } else {
                EnemyTarget.enemyPos = Entity->get_position();
                EnemyTarget.moveForward = Entity->get_logicMoveForward();
                EnemyTarget.isMoving = Entity->isMoving();
            }
        }

        if (Entity != NULL && aimType == 3 && !isCharging) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            minDirection = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }

        if ((Entity != NULL || EnemyTarget.enemyPos != Vector3::Zero()) && get_actorManager == nullptr) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            minDistance = std::numeric_limits<float>::infinity();
            minDirection = std::numeric_limits<float>::infinity();
            minHealth = std::numeric_limits<float>::infinity();
            minHealth2 = std::numeric_limits<float>::infinity();
            minHealthPercent = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }

        if (drawType != 0 && (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || EnemyTarget.ConfigID == 545)) {
if (EnemyTarget.myPos != Vector3::Zero() && EnemyTarget.enemyPos != Vector3::Zero()) {
                Vector3 EnemySC = Camera::get_main()->WorldToScreenPoint(EnemyTarget.enemyPos);


                if (EnemySC.Z > 0) {

                           }
            }
        }
}




    return _UpdateLogic(instance, delta);
}



OBFUSCATE("Category_ "),
        OBFUSCATE("Toggle_Kích hoạt AIM Skill ELSU"),
        OBFUSCATE("RadioButton_Mục Tiêu AIM_Máu thấp nhất,%Máu thấp nhất,Khoảng cách gần nhất"),

          case 9:
            AimElsu = boolean;
            break;

        case 10:
             switch (value) {
             case 1:
                aimType = 1;
                break;
             case 2:
                aimType = 0;
                break;
             case 3:
                aimType = 2;
                break;
             }
             break;


// Phần Hook

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillControlIndicator") , OBFUSCATE("GetUseSkillDirection"), 1), (void *) GetUseSkillDirection, (void **) &_GetUseSkillDirection);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager") , OBFUSCATE("UpdateLogic"), 1), (void *) UpdateLogic, (void **) &_UpdateLogic);

