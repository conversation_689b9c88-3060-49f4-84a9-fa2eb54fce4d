#pragma once
#include <cmath>
#include <limits>
#include "../IL2CppSDKGenerator/Il2Cpp.h"
#include "../IL2CppSDKGenerator/Vector3.h"
#include "../IL2CppSDKGenerator/Quaternion.h"
#include "../../Includes/obfuscate.h"
#include "../Tools/Tools.h"

#define OBFUSCATE_METHOD(image, namespaze, clazz, name, args) \
IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name), args)

#define OBFUSCATE_FIELD(image, namespaze, clazz, name) \
IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name))

// Forward declarations
template<typename T>
class List {
public:
    T* getItems() {
        return *(T**)((uintptr_t)this + 0x10);
    }
    
    int getSize() {
        return *(int*)((uintptr_t)this + 0x18);
    }
};

// Game Classes
class Camera {
    public:
        static Camera *get_main() {
            Camera *(*get_main_) () = (Camera *(*)())OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
            return get_main_();
        }
        
        Vector3 WorldToScreenPoint(Vector3 position) {
            Vector3 (*WorldToScreenPoint_)(Camera *camera, Vector3 position) = (Vector3 (*)(Camera *, Vector3))OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);
            return WorldToScreenPoint_(this, position);
        }
};

class ValueLinkerComponent {
    public:
        int get_actorHp() {
            int (*get_actorHp_)(ValueLinkerComponent * objLinkerWrapper) = (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHp", 0);
            return get_actorHp_(this);
        }

        int get_actorHpTotal() {
            int (*get_actorHpTotal_)(ValueLinkerComponent * objLinkerWrapper) =
                (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHpTotal", 0);
            return get_actorHpTotal_(this);
        }
};

class ActorConfig {
    public:
        int ConfigID() {
            return *(int *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "ActorConfig", "ConfigID"));
        }
};

class ActorLinker {
    public:
        ValueLinkerComponent *ValueComponent() {
            return *(ValueLinkerComponent **)((uintptr_t)this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ValueComponent"));
        }

        ActorConfig *ObjLinker() {
            return *(ActorConfig **) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ObjLinker"));
        }

        Vector3 get_position() {
            Vector3 (*get_position_)(ActorLinker * linker) = (Vector3(*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_position", 0);
            return get_position_(this);
        }
        
        Quaternion get_rotation() {
            Quaternion (*get_rotation_)(ActorLinker *linker) = (Quaternion (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_rotation", 0);
            return get_rotation_(this);
        }
        
        bool IsHostCamp() {
            bool (*IsHostCamp_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostCamp", 0);
            return IsHostCamp_(this);
        }
        
        bool IsHostPlayer() {
            bool (*IsHostPlayer_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostPlayer", 0);
            return IsHostPlayer_(this);
        }
        
        bool isMoving() {
            return *(bool *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "isMoving"));
        }

        Vector3 get_logicMoveForward() {
            Vector3 (*get_logicMoveForward_)(ActorLinker *linker) = (Vector3 (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_logicMoveForward", 0);
            return get_logicMoveForward_(this);
        }
        
        bool get_bVisible() {
            bool (*get_bVisible_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_bVisible", 0);
            return get_bVisible_(this);
        }
};

class ActorManager {
    public:
        List<ActorLinker *> *GetAllHeros() {
            List<ActorLinker *> *(*_GetAllHeros)(ActorManager *actorManager) = (List<ActorLinker *> *(*)(ActorManager *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorManager", "GetAllHeros", 0);
            return _GetAllHeros(this);
        }
};

class KyriosFramework {
    public:
        static ActorManager *get_actorManager() {
            auto get_actorManager_ = (ActorManager *(*)())OBFUSCATE_METHOD("Project_d.dll", "Kyrios", "KyriosFramework", "get_actorManager", 0);
            return get_actorManager_();
        }
};

// Entity Info Structure
struct EntityInfo {
    Vector3 myPos;
    Vector3 enemyPos;
    Vector3 moveForward;
    int ConfigID;
    bool isMoving;
};

// Global variables
extern EntityInfo EnemyTarget;
extern bool AimElsu;
extern bool isCharging;
extern int mode, aimType, drawType, SkillSlott;

// Function declarations
Vector3 RotateVectorByQuaternion(Quaternion q);
float SquaredDistance(Vector3 v, Vector3 o);
Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward);
float getRange(int configID);

// Hook function declarations
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse);
void UpdateLogic(void *instance, int delta);

// Hook function pointers
extern Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse);
extern void (*_UpdateLogic)(void *instance, int delta);

// Initialization function
void InitializeAutoAim();
