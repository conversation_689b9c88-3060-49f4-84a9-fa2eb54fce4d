#pragma once

static auto get_OnCameraHeightChanged(void *player) {
    auto (*_get_OnCameraHeightChanged)(void *player) = (void *(*)(void *))((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem"), OBFUSCATE("OnCameraHeightChanged"), 0));
    return _get_OnCameraHeightChanged(player);
}

void (*LateUpdate)(void *player);
void _LateUpdate(void *player) {
    if (player != nullptr) {
        get_OnCameraHeightChanged(player);
    }
    return LateUpdate(player);
}
float (*GetCameraHeightRateValue)(void *player, int type);
float _GetCameraHeightRateValue(void *player, int type) {
    if (player != nullptr) {
        if (CameraHeight == 0) {
            return 1.5f;
        } else if (CameraHeight == 1) {
            return 1.6f;
        } else if (CameraHeight == 2) {
            return 1.7f;
        } else if (CameraHeight == 3) {
            return 1.8f;
        } else if (CameraHeight == 4) {
            return 1.9f;
        } else if (CameraHeight == 5) {
            return 2.0f;
        } else if (CameraHeight == 6) {
            return 2.1f;
        } else if (CameraHeight == 7) {
            return 2.2f;
        } else if (CameraHeight == 8) {
            return 2.3f;
        } else if (CameraHeight == 9) {
            return 2.4f;
        } else if (CameraHeight == 10) {
            return 2.5f;
        } else if (CameraHeight == 11) {
            return 2.6f;
        } else if (CameraHeight == 12) {
            return 2.7f;
        } else if (CameraHeight == 13) {
            return 2.8f;
        } else if (CameraHeight == 14) {
            return 2.9f;
        } else if (CameraHeight == 15) {
            return 3.0f;
        }
    }
    return GetCameraHeightRateValue(player, type);
}


  /////FPS 60////

bool (*old_get_Supported60FPSMode)(void *instance);
bool get_Supported60FPSMode(void *instance) {
  //  if (instance != NULL && FPS60) 
    {
        return true;
    }
    return old_get_Supported60FPSMode(instance);

}






