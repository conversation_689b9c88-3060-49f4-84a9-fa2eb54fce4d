#pragma once
#include <limits>
#include <cmath>

static auto get_OnCameraHeightChanged(void *player) {
    auto (*_get_OnCameraHeightChanged)(void *player) = (void *(*)(void *))((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem"), OBFUSCATE("OnCameraHeightChanged"), 0));
    return _get_OnCameraHeightChanged(player);
}

void (*LateUpdate)(void *player);
void _LateUpdate(void *player) {
    if (player != nullptr) {
        get_OnCameraHeightChanged(player);
    }
    return LateUpdate(player);
}
float (*GetCameraHeightRateValue)(void *player, int type);
float _GetCameraHeightRateValue(void *player, int type) {
    if (player != nullptr) {
        if (CameraHeight == 0) {
            return 1.5f;
        } else if (CameraHeight == 1) {
            return 1.6f;
        } else if (CameraHeight == 2) {
            return 1.7f;
        } else if (CameraHeight == 3) {
            return 1.8f;
        } else if (CameraHeight == 4) {
            return 1.9f;
        } else if (CameraHeight == 5) {
            return 2.0f;
        } else if (CameraHeight == 6) {
            return 2.1f;
        } else if (CameraHeight == 7) {
            return 2.2f;
        } else if (CameraHeight == 8) {
            return 2.3f;
        } else if (CameraHeight == 9) {
            return 2.4f;
        } else if (CameraHeight == 10) {
            return 2.5f;
        } else if (CameraHeight == 11) {
            return 2.6f;
        } else if (CameraHeight == 12) {
            return 2.7f;
        } else if (CameraHeight == 13) {
            return 2.8f;
        } else if (CameraHeight == 14) {
            return 2.9f;
        } else if (CameraHeight == 15) {
            return 3.0f;
        }
    }
    return GetCameraHeightRateValue(player, type);
}


  /////FPS 60////

bool (*old_get_Supported60FPSMode)(void *instance);
bool get_Supported60FPSMode(void *instance) {
  //  if (instance != NULL && FPS60)
    {
        return true;
    }
    return old_get_Supported60FPSMode(instance);

}

// ===== AUTO AIM CLASSES =====
// Extended ActorLinker class with auto-aim specific methods
class AutoAimActorLinker {
    public:
        static Vector3 get_position(void* instance) {
            Vector3 (*get_position_)(void * linker) = (Vector3(*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_position"), 0);
            return get_position_(instance);
        }

        static Quaternion get_rotation(void* instance) {
            Quaternion (*get_rotation_)(void *linker) = (Quaternion (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_rotation"), 0);
            return get_rotation_(instance);
        }

        static bool IsHostCamp(void* instance) {
            bool (*IsHostCamp_)(void *linker) = (bool (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("IsHostCamp"), 0);
            return IsHostCamp_(instance);
        }

        static bool IsHostPlayer(void* instance) {
            bool (*IsHostPlayer_)(void *linker) = (bool (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("IsHostPlayer"), 0);
            return IsHostPlayer_(instance);
        }

        static bool isMoving(void* instance) {
            return *(bool *) ((uintptr_t) instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("isMoving")));
        }

        static Vector3 get_logicMoveForward(void* instance) {
            Vector3 (*get_logicMoveForward_)(void *linker) = (Vector3 (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_logicMoveForward"), 0);
            return get_logicMoveForward_(instance);
        }

        static void* ValueComponent(void* instance) {
            return *(void **)((uintptr_t)instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("ValueComponent")));
        }

        static void* ObjLinker(void* instance) {
            return *(void **) ((uintptr_t) instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("ActorLinker"), OBFUSCATE("ObjLinker")));
        }
};

class AutoAimValueComponent {
    public:
        static int get_actorHp(void* instance) {
            int (*get_actorHp_)(void * objLinkerWrapper) = (int (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ValueLinkerComponent"), OBFUSCATE("get_actorHp"), 0);
            return get_actorHp_(instance);
        }

        static int get_actorHpTotal(void* instance) {
            int (*get_actorHpTotal_)(void * objLinkerWrapper) = (int (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ValueLinkerComponent"), OBFUSCATE("get_actorHpTotal"), 0);
            return get_actorHpTotal_(instance);
        }
};

class AutoAimActorConfig {
    public:
        static int ConfigID(void* instance) {
            return *(int *) ((uintptr_t) instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("ActorConfig"), OBFUSCATE("ConfigID")));
        }
};

class AutoAimActorManager {
    public:
        static List<void*> *GetAllHeros(void* instance) {
            List<void*> *(*_GetAllHeros)(void *actorManager) = (List<void*> *(*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorManager"), OBFUSCATE("GetAllHeros"), 0);
            return _GetAllHeros(instance);
        }
};

class AutoAimKyriosFramework {
    public:
        static void* get_actorManager() {
            auto get_actorManager_ = (void* (*)())IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios"), OBFUSCATE("KyriosFramework"), OBFUSCATE("get_actorManager"), 0);
            return get_actorManager_();
        }
};

// ===== AUTO AIM UTILITY FUNCTIONS =====
Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.w, x = q.x, y = q.y, z = q.z;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.x - o.x) * (v.x - o.x) + (v.y - o.y) * (v.y - o.y) + (v.z - o.z) * (v.z - o.z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {
        enemyPosi += moveForward;
    }
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

float getRange(int configID) {
    switch(configID) {
        case 196: return 25.f;  // Elsu
        case 108: return 13.f;  // Other heroes
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}

// ===== AUTO AIM HOOK FUNCTIONS =====
Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse) = nullptr;
void (*_UpdateLogic)(void *instance, int delta) = nullptr;

Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != nullptr && AimElsu) {
        if (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 ||
            EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 ||
            EnemyTarget.ConfigID == 545) {
            if (EnemyTarget.myPos != Vector3::zero() &&
                EnemyTarget.enemyPos != Vector3::zero() &&
                SkillSlott == 2) {
                return calculateSkillDirection(EnemyTarget.myPos, EnemyTarget.enemyPos,
                                             EnemyTarget.isMoving, EnemyTarget.moveForward);
            }
        }
    }

    if (_GetUseSkillDirection != nullptr) {
        return _GetUseSkillDirection(instance, isTouchUse);
    }

    return Vector3::zero();
}

void UpdateLogic(void *instance, int delta) {
    if (instance != nullptr) {
        // Get charging state and skill slot
        try {
            isCharging = *(bool *)((uintptr_t)instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager"), OBFUSCATE("m_isCharging")));
            SkillSlott = *(int *)((uintptr_t)instance + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager"), OBFUSCATE("m_currentSkillSlotType")));
        } catch (...) {
            // Handle potential memory access errors
            isCharging = false;
            SkillSlott = 0;
        }
    }

    if (AimElsu) {
        Quaternion rotation;
        float minDistance = std::numeric_limits<float>::infinity();
        float minDirection = std::numeric_limits<float>::infinity();
        float minHealth = std::numeric_limits<float>::infinity();
        float minHealth2 = std::numeric_limits<float>::infinity();
        float minHealthPercent = std::numeric_limits<float>::infinity();
        void *Entity = nullptr;

        try {
            void *get_actorManager = AutoAimKyriosFramework::get_actorManager();
            if (get_actorManager == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            List<void*> *GetAllHeros = AutoAimActorManager::GetAllHeros(get_actorManager);
            if (GetAllHeros == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            void **actorLinkers = (void **) GetAllHeros->getItems();
            if (actorLinkers == nullptr) {
                if (_UpdateLogic != nullptr) {
                    _UpdateLogic(instance, delta);
                }
                return;
            }

            for (int i = 0; i < GetAllHeros->getSize(); i++) {
                void *actorLinker = actorLinkers[(i * 2) + 1];
                if (actorLinker == nullptr) continue;

                if (AutoAimActorLinker::IsHostPlayer(actorLinker)) {
                    rotation = AutoAimActorLinker::get_rotation(actorLinker);
                    EnemyTarget.myPos = AutoAimActorLinker::get_position(actorLinker);
                    void* objLinker = AutoAimActorLinker::ObjLinker(actorLinker);
                    if (objLinker != nullptr) {
                        EnemyTarget.ConfigID = AutoAimActorConfig::ConfigID(objLinker);
                    }
                }

                if (AutoAimActorLinker::IsHostCamp(actorLinker)) continue;

                void* valueComponent = AutoAimActorLinker::ValueComponent(actorLinker);
                if (valueComponent == nullptr) continue;

                int hp = AutoAimValueComponent::get_actorHp(valueComponent);
                if (hp < 1) continue;

                Vector3 EnemyPos = AutoAimActorLinker::get_position(actorLinker);
                float Health = (float)hp;
                float MaxHealth = (float)AutoAimValueComponent::get_actorHpTotal(valueComponent);

                if (MaxHealth <= 0) continue; // Avoid division by zero

                int HealthPercent = (int)std::round(Health / MaxHealth * 100);
                float Distance = Vector3::Distance(EnemyTarget.myPos, EnemyPos);
                float Direction = SquaredDistance(RotateVectorByQuaternion(rotation),
                                                calculateSkillDirection(EnemyTarget.myPos, EnemyPos,
                                                                      AutoAimActorLinker::isMoving(actorLinker),
                                                                      AutoAimActorLinker::get_logicMoveForward(actorLinker)));

                float range = getRange(EnemyTarget.ConfigID);
                if (Distance < range) {
                    if (aimType == 0) {
                        if (HealthPercent < minHealthPercent) {
                            Entity = actorLinker;
                            minHealthPercent = HealthPercent;
                        }

                        if (HealthPercent == minHealthPercent && Health < minHealth2) {
                            Entity = actorLinker;
                            minHealth2 = Health;
                            minHealthPercent = HealthPercent;
                        }
                    }

                    if (aimType == 1 && Health < minHealth) {
                        Entity = actorLinker;
                        minHealth = Health;
                    }

                    if (aimType == 2 && Distance < minDistance) {
                        Entity = actorLinker;
                        minDistance = Distance;
                    }

                    if (aimType == 3 && Direction < minDirection && isCharging) {
                        Entity = actorLinker;
                        minDirection = Direction;
                    }
                }
            }
        } catch (...) {
            // Handle any exceptions during target selection
            Entity = nullptr;
        }

        // Reset target if no valid entity found
        if (Entity == nullptr) {
            EnemyTarget.enemyPos = Vector3::zero();
            EnemyTarget.moveForward = Vector3::zero();
            EnemyTarget.ConfigID = 0;
            EnemyTarget.isMoving = false;
        }

        // Update target information
        if (Entity != nullptr) {
            try {
                float nDistance = Vector3::Distance(EnemyTarget.myPos, AutoAimActorLinker::get_position(Entity));
                void* valueComponent = AutoAimActorLinker::ValueComponent(Entity);
                int hp = (valueComponent != nullptr) ? AutoAimValueComponent::get_actorHp(valueComponent) : 0;

                if (nDistance > getRange(EnemyTarget.ConfigID) || hp < 1) {
                    EnemyTarget.enemyPos = Vector3::zero();
                    EnemyTarget.moveForward = Vector3::zero();
                    Entity = nullptr;
                } else {
                    EnemyTarget.enemyPos = AutoAimActorLinker::get_position(Entity);
                    EnemyTarget.moveForward = AutoAimActorLinker::get_logicMoveForward(Entity);
                    EnemyTarget.isMoving = AutoAimActorLinker::isMoving(Entity);
                }
            } catch (...) {
                // Handle potential errors when accessing entity data
                EnemyTarget.enemyPos = Vector3::zero();
                EnemyTarget.moveForward = Vector3::zero();
                Entity = nullptr;
            }
        }

        // Special handling for aim type 3 (direction-based) when not charging
        if (Entity != nullptr && aimType == 3 && !isCharging) {
            EnemyTarget.enemyPos = Vector3::zero();
            EnemyTarget.moveForward = Vector3::zero();
            Entity = nullptr;
        }
    }

    // Call original function
    if (_UpdateLogic != nullptr) {
        _UpdateLogic(instance, delta);
    }
}






